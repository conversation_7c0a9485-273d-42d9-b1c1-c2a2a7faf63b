#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Export Utilities for Agevolami PM
"""

import os
import tempfile
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.graphics.shapes import Drawing, Rect
from reportlab.graphics import renderPDF

from core import get_logger

logger = get_logger(__name__)

# Agevolami Brand Colors (based on logo)
class AgevoBrandColors:
    """Agevolami brand colors from the logo"""
    AGEVO_BLUE = colors.Color(52/255, 152/255, 219/255)      # Primary blue from logo
    AGEVO_GREEN = colors.Color(39/255, 174/255, 96/255)      # Green accent from logo  
    AGEVO_RED = colors.Color(231/255, 76/255, 60/255)        # Red accent from logo
    AGEVO_DARK_BLUE = colors.Color(44/255, 62/255, 80/255)   # Dark blue for text
    AGEVO_LIGHT_GRAY = colors.Color(236/255, 240/255, 241/255) # Light background
    AGEVO_DARK_GRAY = colors.Color(52/255, 73/255, 94/255)   # Dark text
    WHITE = colors.white
    BLACK = colors.black

class PDFExportUtils:
    """Utility class for PDF export operations with Agevolami branding"""
    
    @staticmethod
    def get_logo_path() -> Optional[str]:
        """Get the path to the Agevolami logo"""
        try:
            # Check multiple possible locations for the logo
            possible_paths = [
                "assets/logo_v2.png",                    # Development
                "../assets/logo_v2.png",                 # Alternative dev path
                "../../assets/logo_v2.png",              # Alternative dev path
                os.path.join(os.path.dirname(__file__), "../../../assets/logo_v2.png"),  # Relative from utils
                "logo_v2.png",                           # Compiled app root
                "assets/logo_v2.png",                    # Compiled app assets
            ]
            
            for path in possible_paths:
                full_path = os.path.abspath(path)
                if os.path.exists(full_path):
                    logger.info(f"Found logo at: {full_path}")
                    return full_path
            
            logger.warning("Logo file not found in any expected location")
            return None
            
        except Exception as e:
            logger.error(f"Error finding logo: {e}")
            return None
    
    @staticmethod
    def truncate_text(text: str, max_length: int) -> str:
        """Truncate text if too long, add ellipsis"""
        if not text:
            return "N/A"
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."
    
    @staticmethod
    def wrap_text_for_cell(text: str, max_length: int) -> str:
        """Wrap text for table cells with line breaks"""
        if not text:
            return "N/A"
        if len(text) <= max_length:
            return text
        
        # Split text into chunks
        chunks = []
        words = text.split()
        current_chunk = ""
        
        for word in words:
            if len(current_chunk + " " + word) <= max_length:
                current_chunk += (" " + word) if current_chunk else word
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = word
        
        if current_chunk:
            chunks.append(current_chunk)
        
        # If too many chunks, truncate and add ellipsis
        if len(chunks) > 3:
            chunks = chunks[:3]
            chunks[-1] += "..."
        
        return "<br/>".join(chunks)
    
    @staticmethod
    def get_branded_styles():
        """Get Agevolami branded PDF styles"""
        styles = getSampleStyleSheet()
        
        # Main title style with Agevolami branding
        title_style = ParagraphStyle(
            'AgevoBrandedTitle',
            parent=styles['Heading1'],
            fontSize=20,
            spaceAfter=20,
            spaceBefore=10,
            alignment=TA_CENTER,
            textColor=AgevoBrandColors.AGEVO_DARK_BLUE,
            fontName='Helvetica-Bold'
        )
        
        # Subtitle style
        subtitle_style = ParagraphStyle(
            'AgevoSubtitle',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=15,
            spaceBefore=10,
            alignment=TA_CENTER,
            textColor=AgevoBrandColors.AGEVO_BLUE,
            fontName='Helvetica-Bold'
        )
        
        # App name style
        app_name_style = ParagraphStyle(
            'AgevoAppName',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=5,
            alignment=TA_CENTER,
            textColor=AgevoBrandColors.AGEVO_DARK_GRAY,
            fontName='Helvetica-Bold'
        )
        
        # Cell style for table content
        cell_style = ParagraphStyle(
            'AgevoCellStyle',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_LEFT,
            wordWrap='CJK',
            allowWidows=1,
            allowOrphans=1,
            textColor=AgevoBrandColors.AGEVO_DARK_GRAY
        )
        
        # Summary section style
        summary_style = ParagraphStyle(
            'AgevoSummaryStyle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=10,
            spaceBefore=15,
            textColor=AgevoBrandColors.AGEVO_DARK_BLUE,
            fontName='Helvetica-Bold'
        )
        
        # Footer style
        footer_style = ParagraphStyle(
            'AgevoFooterStyle',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=AgevoBrandColors.AGEVO_DARK_GRAY,
            spaceAfter=5
        )
        
        return {
            'base': styles,
            'title': title_style,
            'subtitle': subtitle_style,
            'app_name': app_name_style,
            'cell': cell_style,
            'summary': summary_style,
            'footer': footer_style
        }
    
    @staticmethod
    def get_branded_table_style():
        """Get Agevolami branded table style configuration"""
        return TableStyle([
            # Header styling with brand colors
            ('BACKGROUND', (0, 0), (-1, 0), AgevoBrandColors.AGEVO_DARK_BLUE),
            ('TEXTCOLOR', (0, 0), (-1, 0), AgevoBrandColors.WHITE),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('TOPPADDING', (0, 1), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 4),
            ('LEFTPADDING', (0, 0), (-1, -1), 4),
            ('RIGHTPADDING', (0, 0), (-1, -1), 4),
            
            # Body styling with alternating brand colors
            ('BACKGROUND', (0, 1), (-1, -1), AgevoBrandColors.WHITE),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('TEXTCOLOR', (0, 1), (-1, -1), AgevoBrandColors.AGEVO_DARK_GRAY),
            ('GRID', (0, 0), (-1, -1), 1, AgevoBrandColors.AGEVO_DARK_BLUE),
            
            # Alternating row colors with subtle brand tints
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [
                AgevoBrandColors.WHITE, 
                AgevoBrandColors.AGEVO_LIGHT_GRAY
            ])
        ])
    
    @staticmethod
    def create_temp_pdf_path(prefix: str) -> str:
        """Create temporary PDF file path"""
        temp_dir = tempfile.gettempdir()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"agevolami_{prefix}_export_{timestamp}.pdf"
        return os.path.join(temp_dir, filename)
    
    @staticmethod
    def add_branded_header(story: List, title: str, count: int, styles: Dict, export_type: str = ""):
        """Add branded document header with logo and app information"""
        
        # Try to add logo
        logo_path = PDFExportUtils.get_logo_path()
        if logo_path and os.path.exists(logo_path):
            try:
                # Create a table for logo and app info layout
                logo_img = Image(logo_path, width=1.5*inch, height=0.6*inch)
                
                # App info text
                app_info = f"<b>AGEVOLAMI PM</b><br/>Project Management System"
                app_info_para = Paragraph(app_info, styles['app_name'])
                
                # Create header table with logo and app info
                header_data = [[logo_img, app_info_para]]
                header_table = Table(header_data, colWidths=[2*inch, 4*inch])
                header_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (0, 0), 'LEFT'),   # Logo left aligned
                    ('ALIGN', (1, 0), (1, 0), 'RIGHT'),  # App info right aligned
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                    ('TOPPADDING', (0, 0), (-1, -1), 5),
                ]))
                
                story.append(header_table)
                story.append(Spacer(1, 10))
                
            except Exception as e:
                logger.warning(f"Could not add logo to PDF: {e}")
                # Fallback to text-only header
                PDFExportUtils._add_text_only_header(story, styles)
        else:
            # Fallback to text-only header
            PDFExportUtils._add_text_only_header(story, styles)
        
        # Add main title with brand styling
        story.append(Paragraph(title, styles['title']))
        
        # Add colorful subtitle based on export type
        if export_type:
            subtitle_color = AgevoBrandColors.AGEVO_GREEN if "task" in export_type.lower() else AgevoBrandColors.AGEVO_RED
            subtitle_style = ParagraphStyle(
                'ColoredSubtitle',
                parent=styles['subtitle'],
                textColor=subtitle_color
            )
            story.append(Paragraph(f"Report {export_type.title()}", subtitle_style))
        
        # Document info with brand styling
        info_text = f"""
        <b>Generato il:</b> {datetime.now().strftime('%d/%m/%Y alle %H:%M')}<br/>
        <b>Totale elementi:</b> {count}<br/>
        <b>Sistema:</b> Agevolami PM v2.0
        """
        
        info_style = ParagraphStyle(
            'AgevoInfoStyle',
            parent=styles['base']['Normal'],
            fontSize=10,
            textColor=AgevoBrandColors.AGEVO_DARK_GRAY,
            spaceAfter=20
        )
        
        story.append(Paragraph(info_text, info_style))
        story.append(Spacer(1, 15))
    
    @staticmethod
    def _add_text_only_header(story: List, styles: Dict):
        """Add text-only header when logo is not available"""
        header_text = """
        <b><font size="16" color="#2c3e50">AGEVOLAMI</font></b><br/>
        <font size="10" color="#34495e">Project Management System</font>
        """
        header_para = Paragraph(header_text, styles['app_name'])
        story.append(header_para)
        story.append(Spacer(1, 10))
    
    @staticmethod
    def add_branded_footer(story: List, styles: Dict):
        """Add branded footer to the document"""
        footer_text = f"""
        <br/><br/>
        ───────────────────────────────────────────────────────────<br/>
        <b>Agevolami PM</b> - Sistema di Gestione Progetti<br/>
        Generato automaticamente il {datetime.now().strftime('%d/%m/%Y alle %H:%M')}<br/>
        <i>Questo documento è stato creato con Agevolami Project Management System</i>
        """
        
        story.append(Paragraph(footer_text, styles['footer']))
    
    @staticmethod
    def add_statistics_summary(story: List, data: List[Dict[str, Any]], styles: Dict, 
                             summary_config: Dict[str, Any]):
        """Add statistics summary section with brand styling"""
        if not data:
            return
        
        story.append(Spacer(1, 20))
        
        # Add colored section divider
        divider_text = "📊 RIEPILOGO STATISTICHE"
        story.append(Paragraph(divider_text, styles['summary']))
        
        summary_text = ""
        
        # Add configured statistics
        for section_name, field_name in summary_config.get('counts', {}).items():
            counts = {}
            for item in data:
                value = item.get(field_name, 'N/A')
                if hasattr(value, 'value'):
                    value = value.value
                elif hasattr(value, '__str__'):
                    value = str(value)
                counts[value] = counts.get(value, 0) + 1
            
            summary_text += f"<b><font color='#{AgevoBrandColors.AGEVO_BLUE.hexval()}'>{section_name}:</font></b><br/>"
            for value, count in counts.items():
                summary_text += f"• {value.title()}: <b>{count}</b><br/>"
            summary_text += "<br/>"
        
        # Add custom statistics if provided
        if 'custom_stats' in summary_config:
            summary_text += f"<b><font color='#{AgevoBrandColors.AGEVO_GREEN.hexval()}'>Analisi Aggiuntive:</font></b><br/>"
            for stat_name, stat_value in summary_config['custom_stats'].items():
                summary_text += f"• {stat_name}: <b>{stat_value}</b><br/>"
        
        story.append(Paragraph(summary_text, styles['base']['Normal']))

class PDFTableBuilder:
    """Builder class for creating branded PDF tables"""
    
    def __init__(self, headers: List[str], column_configs: List[Dict[str, Any]]):
        """
        Initialize table builder with Agevolami branding
        
        Args:
            headers: List of column headers
            column_configs: List of column configurations with keys:
                - width: Column width in inches
                - text_length: Max text length for truncation
                - wrap: Whether to wrap text
        """
        self.headers = headers
        self.column_configs = column_configs
        self.data = [headers]
        self.styles = PDFExportUtils.get_branded_styles()
    
    def add_row(self, row_data: List[str]) -> None:
        """Add a row to the table with proper text formatting"""
        formatted_row = []
        
        for i, (cell_data, config) in enumerate(zip(row_data, self.column_configs)):
            if config.get('wrap', False):
                # Use wrapping for long text
                formatted_text = PDFExportUtils.wrap_text_for_cell(
                    cell_data or "N/A", 
                    config.get('text_length', 25)
                )
                formatted_row.append(Paragraph(formatted_text, self.styles['cell']))
            else:
                # Use truncation for shorter fields
                formatted_text = PDFExportUtils.truncate_text(
                    cell_data or "N/A", 
                    config.get('text_length', 20)
                )
                formatted_row.append(formatted_text)
        
        self.data.append(formatted_row)
    
    def build(self) -> Table:
        """Build the table with Agevolami branding"""
        # Extract column widths
        col_widths = [config['width'] * inch for config in self.column_configs]
        
        # Create table
        table = Table(self.data, colWidths=col_widths)
        table.setStyle(PDFExportUtils.get_branded_table_style())
        
        return table

def export_to_pdf(
    data: List[Dict[str, Any]], 
    filename_prefix: str,
    title: str,
    column_config: Dict[str, Any],
    summary_config: Optional[Dict[str, Any]] = None,
    export_type: str = ""
) -> str:
    """
    Generic PDF export function with Agevolami branding
    
    Args:
        data: List of dictionaries containing the data to export
        filename_prefix: Prefix for the PDF filename
        title: Title for the PDF document
        column_config: Configuration for table columns
        summary_config: Configuration for statistics summary
        export_type: Type of export (tasks, deadlines, etc.)
        
    Returns:
        Path to the generated PDF file
    """
    try:
        # Create PDF path
        pdf_path = PDFExportUtils.create_temp_pdf_path(filename_prefix)
        
        # Create document with Agevolami margins
        doc = SimpleDocTemplate(
            pdf_path, 
            pagesize=A4,
            topMargin=0.8*inch,
            bottomMargin=0.8*inch,
            leftMargin=0.7*inch,
            rightMargin=0.7*inch
        )
        story = []
        styles = PDFExportUtils.get_branded_styles()
        
        # Add branded header
        PDFExportUtils.add_branded_header(story, title, len(data), styles, export_type)
        
        # Create table if data exists
        if data:
            table_builder = PDFTableBuilder(
                column_config['headers'],
                column_config['columns']
            )
            
            # Add data rows
            for item in data:
                row_data = [str(item.get(field, '')) for field in column_config['fields']]
                table_builder.add_row(row_data)
            
            # Add table to story
            story.append(table_builder.build())
            
            # Add statistics summary if configured
            if summary_config:
                PDFExportUtils.add_statistics_summary(story, data, styles, summary_config)
        else:
            no_data_text = "Nessun elemento trovato con i filtri selezionati."
            story.append(Paragraph(no_data_text, styles['base']['Normal']))
        
        # Add branded footer
        PDFExportUtils.add_branded_footer(story, styles)
        
        # Generate PDF
        doc.build(story)
        logger.info(f"Branded PDF generated: {pdf_path}")
        return pdf_path
        
    except Exception as e:
        logger.error(f"Error generating branded PDF: {e}")
        raise e 